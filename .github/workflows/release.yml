# .github/workflows/release.yml

# Need to write to repo contents to upload the app to GitHub Release
# See: https://www.electronforge.io/config/publishers/github#authentication
permissions:
  contents: write

name: Release app
on:
  workflow_dispatch:
jobs:
  build:
    environment: release
    strategy:
      # Uncomment max-parallel to prevent race condition (where multiple releases are
      # created concurrently). Typically though, we'll create a release manually ahead of time
      # which prevents the race.
      # max-parallel: 1
      matrix:
        os: [
            { name: "windows", image: "windows-latest" },
            # See https://github.com/dyad-sh/dyad/issues/96
            { name: "linux", image: "ubuntu-22.04" },
            { name: "macos-intel", image: "macos-13" },
            { name: "macos", image: "macos-latest" },
          ]
    runs-on: ${{ matrix.os.image }}
    steps:
      - name: Github checkout
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
      - name: Use Node.js
        uses: actions/setup-node@cdca7365b2dadb8aad0a33bc7601856ffabcc48e # v4.3.0
        with:
          node-version: 20
      - run: npm ci
      - name: add macos cert
        if: contains(matrix.os.name, 'macos')
        env:
          MACOS_CERT_P12: ${{ secrets.MACOS_CERT_P12 }}
          MACOS_CERT_PASSWORD: ${{ secrets.MACOS_CERT_PASSWORD }}
        run: chmod +x tools/add-macos-cert.sh && . ./tools/add-macos-cert.sh
      # Windows only
      - name: Set up certificate
        if: contains(matrix.os.name, 'windows')
        run: |
          echo "${{ secrets.SM_CLIENT_CERT_FILE_B64 }}" | base64 --decode > /d/Certificate_pkcs12.p12
        shell: bash
      - name: Set variables
        if: contains(matrix.os.name, 'windows')
        id: variables
        run: |
          echo "SM_HOST=${{ secrets.SM_HOST }}" >> "$GITHUB_ENV" 
          echo "SM_API_KEY=${{ secrets.SM_API_KEY }}" >> "$GITHUB_ENV" 
          echo "SM_CLIENT_CERT_FILE=D:\\Certificate_pkcs12.p12" >> "$GITHUB_ENV" 
          echo "SM_CLIENT_CERT_PASSWORD=${{ secrets.SM_CLIENT_CERT_PASSWORD }}" >> "$GITHUB_ENV"
        shell: bash
      - name: Code signing with Software Trust Manager
        if: contains(matrix.os.name, 'windows')
        uses: digicert/ssm-code-signing@v1.1.0
      - name: Sync certificate (Windows)
        if: contains(matrix.os.name, 'windows')
        run: |
          smctl windows certsync --keypair-alias=${{ secrets.DIGICERT_KEYPAIR_ALIAS }}
        shell: bash
        # Publish (all platforms)
      - name: Publish app
        env:
          NODE_OPTIONS: "--max-old-space-size=4096"
          SM_CODE_SIGNING_CERT_SHA1_HASH: ${{ secrets.SM_CODE_SIGNING_CERT_SHA1_HASH }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
          APPLE_ID: ${{ secrets.APPLE_ID }}
          APPLE_PASSWORD: ${{ secrets.APPLE_PASSWORD }}
        run: npm run publish

  verify-assets:
    name: Verify Release Assets
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Github checkout
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
      - name: Use Node.js
        uses: actions/setup-node@cdca7365b2dadb8aad0a33bc7601856ffabcc48e # v4.3.0
        with:
          node-version: 20
      - name: Verify all release assets are uploaded
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: node scripts/verify-release-assets.js
