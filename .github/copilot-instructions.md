# DYAD - Copilot Instructions

<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

## Project Overview
DYAD is a local, open-source AI app builder built with Electron, similar to Lovable, v0, or Bolt, but running locally on your machine.

## Key Technologies
- **Framework**: Electron with Vite
- **Language**: TypeScript
- **Database**: SQLite with Drizzle ORM  
- **UI**: Custom UI components
- **Build Tool**: Vite for bundling

## Development Commands
- `npm start` - Start the development server
- `npm run build` - Build the application
- `npm test` - Run unit tests
- `npm run e2e` - Run end-to-end tests

## Architecture
- Main process: Handles IPC, database operations, and window management
- Renderer process: UI components and user interactions  
- Workers: Background processing (TypeScript compilation, etc.)
- Database: SQLite with <PERSON><PERSON><PERSON> ORM for data persistence

## Important Directories
- `src/` - Main application source code
- `workers/` - Worker processes
- `drizzle/` - Database migrations and schema
- `shared/` - Shared utilities and types
- `assets/` - Static assets

## Development Notes
- The app supports both local and cloud AI providers
- Built-in TypeScript compilation and error checking
- Automatic backup system for user data
- Cross-platform support (macOS, Windows, Linux)
