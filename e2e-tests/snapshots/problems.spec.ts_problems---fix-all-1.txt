===
role: user
message: Fix these 3 TypeScript compile-time errors:

1. src/bad-file.tsx:2:1 - Cannot find name 'nonExistentFunction1'. (TS2304)
```
const App = () => <div>Minimal imported app</div>;
nonExistentFunction1(); // <-- TypeScript compiler error here
nonExistentFunction2();
```

2. src/bad-file.tsx:3:1 - Cannot find name 'nonExistentFunction2'. (TS2304)
```
nonExistentFunction1();
nonExistentFunction2(); // <-- TypeScript compiler error here
nonExistentFunction3();
```

3. src/bad-file.tsx:4:1 - Cannot find name 'nonExistentFunction3'. (TS2304)
```
nonExistentFunction2();
nonExistentFunction3(); // <-- TypeScript compiler error here
```


Please fix all errors in a concise way.