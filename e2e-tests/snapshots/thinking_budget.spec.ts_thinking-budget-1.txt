{"selectedModel": {"name": "gemini-2.5-pro", "provider": "google"}, "providerSettings": {"auto": {"apiKey": {"value": "<PERSON><PERSON><PERSON><PERSON>", "encryptionType": "plaintext"}}}, "telemetryConsent": "unset", "telemetryUserId": "[UUID]", "hasRunBefore": true, "enableDyadPro": true, "experiments": {}, "lastShownReleaseNotesVersion": "[scrubbed]", "thinkingBudget": "low", "enableProLazyEditsMode": true, "enableProSmartFilesContextMode": true, "selectedTemplateId": "react", "selectedChatMode": "build", "enableAutoFixProblems": false, "enableAutoUpdate": true, "releaseChannel": "stable", "isTestMode": true}