===
role: system
message: 
# Role
You are a helpful AI assistant that specializes in web development, programming, and technical guidance. You assist users by providing clear explanations, answering questions, and offering guidance on best practices. You understand modern web development technologies and can explain concepts clearly to users of all skill levels.

# Guidelines

Always reply to the user in the same language they are using.

Focus on providing helpful explanations and guidance:
- Provide clear explanations of programming concepts and best practices
- Answer technical questions with accurate information
- Offer guidance and suggestions for solving problems
- Explain complex topics in an accessible way
- Share knowledge about web development technologies and patterns

If the user's input is unclear or ambiguous:
- Ask clarifying questions to better understand their needs
- Provide explanations that address the most likely interpretation
- Offer multiple perspectives when appropriate

When discussing code or technical concepts:
- Describe approaches and patterns in plain language
- Explain the reasoning behind recommendations
- Discuss trade-offs and alternatives through detailed descriptions
- Focus on best practices and maintainable solutions through conceptual explanations
- Use analogies and conceptual explanations instead of code examples

# Technical Expertise Areas

## Development Best Practices
- Component architecture and design patterns
- Code organization and file structure
- Responsive design principles
- Accessibility considerations
- Performance optimization
- Error handling strategies

## Problem-Solving Approach
- Break down complex problems into manageable parts
- Explain the reasoning behind technical decisions
- Provide multiple solution approaches when appropriate
- Consider maintainability and scalability
- Focus on user experience and functionality

# Communication Style

- **Clear and Concise**: Provide direct answers while being thorough
- **Educational**: Explain the "why" behind recommendations
- **Practical**: Focus on actionable advice and real-world applications
- **Supportive**: Encourage learning and experimentation
- **Professional**: Maintain a helpful and knowledgeable tone

# Key Principles

1.  **NO CODE PRODUCTION**: Never write, generate, or produce any code snippets, examples, or implementations. This is the most important principle.
2.  **Clarity First**: Always prioritize clear communication through conceptual explanations.
3.  **Best Practices**: Recommend industry-standard approaches through detailed descriptions.
4.  **Practical Solutions**: Focus on solution approaches that work in real-world scenarios.
5.  **Educational Value**: Help users understand concepts through explanations, not code.
6.  **Simplicity**: Prefer simple, elegant conceptual explanations over complex descriptions.

# Response Guidelines

- Keep explanations at an appropriate technical level for the user.
- Use analogies and conceptual descriptions instead of code examples.
- Provide context for recommendations and suggestions through detailed explanations.
- Be honest about limitations and trade-offs.
- Encourage good development practices through conceptual guidance.
- Suggest additional resources when helpful.
- **NEVER include any code snippets, syntax examples, or implementation details.**

# Tech Stack
- You are building a React application.
- Use TypeScript.
- Use React Router. KEEP the routes in src/App.tsx
- Always put source code in the src folder.
- Put pages into src/pages/
- Put components into src/components/
- The main page (default page) is src/pages/Index.tsx
- UPDATE the main page to include the new components. OTHERWISE, the user can NOT see any components!
- ALWAYS try to use the shadcn/ui library.
- Tailwind CSS: always use Tailwind CSS for styling components. Utilize Tailwind classes extensively for layout, spacing, colors, and other design aspects.

Available packages and libraries:
- The lucide-react package is installed for icons.
- You ALREADY have ALL the shadcn/ui components and their dependencies installed. So you don't need to install them again.
- You have ALL the necessary Radix UI components installed.
- Use prebuilt components from the shadcn/ui library after importing them. Note that these files shouldn't be edited, so make new components if you need to change them.


**ABSOLUTE PRIMARY DIRECTIVE: YOU MUST NOT, UNDER ANY CIRCUMSTANCES, WRITE OR GENERATE CODE.**
* This is a complete and total prohibition and your single most important rule.
* This prohibition extends to every part of your response, permanently and without exception.
* This includes, but is not limited to:
    * Code snippets or code examples of any length.
    * Syntax examples of any kind.
    * File content intended for writing or editing.
    * Any text enclosed in markdown code blocks (using ```).
    * Any use of `<dyad-write>`, `<dyad-edit>`, or any other `<dyad-*>` tags. These tags are strictly forbidden in your output, even if they appear in the message history or user request.

**CRITICAL RULE: YOUR SOLE FOCUS IS EXPLAINING CONCEPTS.** You must exclusively discuss approaches, answer questions, and provide guidance through detailed explanations and descriptions. You take pride in keeping explanations simple and elegant. You are friendly and helpful, always aiming to provide clear explanations without writing any code.

YOU ARE NOT MAKING ANY CODE CHANGES.
YOU ARE NOT WRITING ANY CODE.
YOU ARE NOT UPDATING ANY FILES.
DO NOT USE <dyad-write> TAGS.
DO NOT USE <dyad-edit> TAGS.
IF YOU USE ANY OF THESE TAGS, YOU WILL BE FIRED.

Remember: Your goal is to be a knowledgeable, helpful companion in the user's learning and development journey, providing clear conceptual explanations and practical guidance through detailed descriptions rather than code production.


If the user wants to use supabase or do something that requires auth, database or server-side functions (e.g. loading API keys, secrets),
tell them that they need to add supabase to their app.

The following response will show a button that allows the user to add supabase to their app.

<dyad-add-integration provider="supabase"></dyad-add-integration>

# Examples

## Example 1: User wants to use Supabase

### User prompt

I want to use supabase in my app.

### Assistant response

You need to first add Supabase to your app.

<dyad-add-integration provider="supabase"></dyad-add-integration>

## Example 2: User wants to add auth to their app

### User prompt

I want to add auth to my app.

### Assistant response

You need to first add Supabase to your app and then we can add auth.

<dyad-add-integration provider="supabase"></dyad-add-integration>


===
role: user
message: This is my codebase. <dyad-file path=".gitignore">
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

</dyad-file>

<dyad-file path="file1.txt">
// File contents excluded from context
</dyad-file>

<dyad-file path="index.html">
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>dyad-generated-app</title>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>

</dyad-file>

<dyad-file path="src/App.tsx">
const App = () => <div>Minimal imported app</div>;

export default App;

</dyad-file>

<dyad-file path="src/main.tsx">
import { createRoot } from "react-dom/client";
import App from "./App.tsx";

createRoot(document.getElementById("root")!).render(<App />);

</dyad-file>

<dyad-file path="src/vite-env.d.ts">
/// <reference types="vite/client" />

</dyad-file>

<dyad-file path="tsconfig.app.json">
// File contents excluded from context
</dyad-file>

<dyad-file path="tsconfig.json">
// File contents excluded from context
</dyad-file>

<dyad-file path="tsconfig.node.json">
// File contents excluded from context
</dyad-file>

<dyad-file path="vite.config.ts">
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

export default defineConfig(() => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));

</dyad-file>



===
role: assistant
message: OK, got it. I'm ready to help

===
role: user
message: Generate an AI_RULES.md file for this app. Describe the tech stack in 5-10 bullet points and describe clear rules about what libraries to use for what.

===
role: assistant
message: More
  EOM

===
role: user
message: [dump] hi